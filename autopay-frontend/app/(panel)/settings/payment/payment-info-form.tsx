'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import useAuth from '@/lib/hooks/useAuth'
import { useDomainData, useUpdateDomain } from '@/lib/hooks/useDomainData'
import { useEffect } from 'react'
import { toast } from 'sonner'

const paymentInfoSchema = z.object({
  bank_name: z.string().optional(),
  account_number: z.string().optional(),
  account_holder: z.string().optional(),
  branch: z.string().optional(),
  swift_code: z.string().optional(),
  tax_code: z.string().optional(),
  company_address: z.string().optional(),
})

type PaymentInfoValues = z.infer<typeof paymentInfoSchema>

const defaultValues: Partial<PaymentInfoValues> = {
  bank_name: '',
  account_number: '',
  account_holder: '',
  branch: '',
  swift_code: '',
  tax_code: '',
  company_address: '',
}

export function PaymentInfoForm() {
  const form = useForm<PaymentInfoValues>({
    resolver: zodResolver(paymentInfoSchema),
    defaultValues,
    mode: 'onChange',
  })

  async function onSubmit(_data: PaymentInfoValues) {
    toast({
      title: 'Thông báo',
      description: 'Chức năng cập nhật thông tin thanh toán đang được phát triển',
    })
    return
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6">
        <Card>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="bank_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên ngân hàng</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Vietcombank"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="account_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số tài khoản</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="**********"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="account_holder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Chủ tài khoản</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="CÔNG TY TNHH ABC"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branch"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Chi nhánh</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Chi nhánh Hà Nội"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="swift_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mã SWIFT</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="BFTVVNVX"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="tax_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mã số thuế</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="0123456789"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="company_address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Địa chỉ công ty</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Số 123, Đường ABC, Phường XYZ, Quận DEF, Thành phố Hà Nội"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Button
          type="submit"
          size="sm">
          Cập nhật thông tin
        </Button>
      </form>
    </Form>
  )
}
