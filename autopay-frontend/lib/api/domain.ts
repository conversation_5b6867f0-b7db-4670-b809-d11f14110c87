import type { Domain } from '@/lib/types/domain'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'

/**
 * Domain API functions for organization domain management
 */

export interface DomainUpdateRequest {
  frontend_hostname?: string
  backend_hostname?: string
  data?: {
    branding?: {
      name?: string
      slogan?: string
      logo_url?: string
      favicon_url?: string
    }
    theme?: {
      name?: string
    }
    seo?: {
      title?: string
      description?: string
      keywords?: string
      og_image?: string
    }
    contact?: Record<string, any>
    custom?: Record<string, any>
  }
  is_active?: boolean
}

export interface DomainCreateRequest extends DomainUpdateRequest {
  organization_id?: string
}

/**
 * Fetch domain for organization
 */
export async function fetchDomain(organizationId: string): Promise<Domain> {
  if (!organizationId) {
    throw new Error('organization_id is required for fetching domain')
  }

  const url = `/${organizationId}/domains`

  const response = await queryFetchHelper(url, {
    timeout: 30000,
  })
  return response.data
}

/**
 * Create domain for organization
 */
export async function createDomain(organizationId: string, request: DomainCreateRequest): Promise<Domain> {
  if (!organizationId) {
    throw new Error('organization_id is required for creating domain')
  }

  const url = `/${organizationId}/domains`

  const response = await queryFetchHelper(url, {
    method: 'POST',
    body: JSON.stringify(request),
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: 30000,
  })
  return response.data
}

/**
 * Update domain for organization
 */
export async function updateDomain(
  organizationId: string,
  domainId: string,
  request: DomainUpdateRequest
): Promise<Domain> {
  if (!organizationId || !domainId) {
    throw new Error('Both organization_id and domain_id are required for updating domain')
  }

  const url = `/${organizationId}/domains/${domainId}`

  const response = await queryFetchHelper(url, {
    method: 'PUT',
    body: JSON.stringify(request),
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: 30000,
  })
  return response.data
}

/**
 * Delete domain for organization
 */
export async function deleteDomain(organizationId: string, domainId: string): Promise<void> {
  if (!organizationId || !domainId) {
    throw new Error('Both organization_id and domain_id are required for deleting domain')
  }

  const url = `/${organizationId}/domains/${domainId}`

  await queryFetchHelper(url, {
    method: 'DELETE',
    timeout: 30000,
  })
}

/**
 * Upload file for domain branding
 */
export async function uploadDomainFile(organizationId: string, formData: FormData): Promise<{ url: string }> {
  if (!organizationId) {
    throw new Error('organization_id is required for uploading domain file')
  }

  const url = `/${organizationId}/domains/upload`

  const response = await queryFetchHelper(url, {
    method: 'POST',
    body: formData,
    timeout: 60000, // Longer timeout for file uploads
  })
  return response.data
}

/**
 * Delete uploaded file for domain branding
 */
export async function deleteDomainFile(organizationId: string, fileUrl: string): Promise<void> {
  if (!organizationId || !fileUrl) {
    throw new Error('Both organization_id and file_url are required for deleting domain file')
  }

  const url = `/${organizationId}/domains/delete-file`

  await queryFetchHelper(url, {
    method: 'POST',
    body: JSON.stringify({ file_url: fileUrl }),
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: 30000,
  })
}

/**
 * Cache keys for React Query
 */
export const DOMAIN_QUERY_KEYS = {
  domain: (organizationId: string) => ['domain', organizationId],
} as const
