'use client'

import { DOMAIN_QUERY_KEYS, fetchDomain, updateDomain, type DomainUpdateRequest } from '@/lib/api/domain'
import type { Domain } from '@/lib/types/domain'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

/**
 * Hook to fetch domain data for organization
 */
export function useDomainData(organizationId: string | undefined) {
  return useQuery({
    queryKey: DOMAIN_QUERY_KEYS.domain(organizationId || ''),
    queryFn: () => fetchDomain(organizationId!),
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to update domain data
 */
export function useUpdateDomain(organizationId: string | undefined) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ domainId, data }: { domainId: string; data: DomainUpdateRequest }) =>
      updateDomain(organizationId!, domainId, data),
    onSuccess: (updatedDomain: Domain) => {
      // Update the cache with new data
      queryClient.setQueryData(DOMAIN_QUERY_KEYS.domain(organizationId!), updatedDomain)
      
      // Show success message
      toast.success('Cập nhật domain thành công')
    },
    onError: (error: any) => {
      // Show error message
      const errorMessage = error?.message || 'Có lỗi xảy ra khi cập nhật domain'
      toast.error(errorMessage)
    },
  })
}
